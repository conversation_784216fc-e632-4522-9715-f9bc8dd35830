["test/test_api_integration.py::TestAPIErrorHandling::test_authentication_expiry_during_operation", "test/test_api_integration.py::TestAPIErrorHandling::test_connection_error", "test/test_api_integration.py::TestAPIErrorHandling::test_empty_api_response", "test/test_api_integration.py::TestAPIErrorHandling::test_malformed_api_response", "test/test_api_integration.py::TestAPIErrorHandling::test_max_retries_exceeded", "test/test_api_integration.py::TestAPIErrorHandling::test_memory_optimization_large_datasets", "test/test_api_integration.py::TestAPIErrorHandling::test_network_timeout_error", "test/test_api_integration.py::TestAPIErrorHandling::test_partial_batch_failure", "test/test_api_integration.py::TestAPIErrorHandling::test_retry_logic_with_exponential_backoff", "test/test_api_integration.py::TestAPIIntegration::test_fyers_authentication_failure", "test/test_api_integration.py::TestAPIIntegration::test_fyers_authentication_success", "test/test_api_integration.py::TestAPIIntegration::test_fyers_client_initialization", "test/test_api_integration.py::TestAPIIntegration::test_get_historical_data_error", "test/test_api_integration.py::TestAPIIntegration::test_get_historical_data_success", "test/test_api_integration.py::TestAPIIntegration::test_get_quotes_api_error", "test/test_api_integration.py::TestAPIIntegration::test_get_quotes_batch_processing", "test/test_api_integration.py::TestAPIIntegration::test_get_quotes_rate_limiting", "test/test_api_integration.py::TestAPIIntegration::test_get_quotes_with_authentication", "test/test_api_integration.py::TestAPIIntegration::test_get_quotes_without_authentication", "test/test_api_integration.py::TestAPIIntegration::test_market_data_parsing_edge_cases", "test/test_api_integration.py::TestAPIIntegration::test_market_data_parsing_zero_values", "test/test_api_integration.py::TestAPIIntegrationWithScanners::test_scanner_api_integration_failure", "test/test_api_integration.py::TestAPIIntegrationWithScanners::test_scanner_api_partial_data_retrieval", "test/test_constant.py::TestConstants::test_constants_are_strings", "test/test_constant.py::TestConstants::test_market_types_constants", "test/test_constant.py::TestConstants::test_market_types_list", "test/test_constant.py::TestConstants::test_market_types_list_is_list", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_complete_workflow_with_all_market_types", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_configuration_validation_end_to_end", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_equity_scanner_end_to_end", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_file_permissions_and_access", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_futures_scanner_end_to_end", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_index_scanner_end_to_end", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_options_scanner_end_to_end", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_output_directory_creation", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_report_generator_file_creation", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_report_generator_initialization", "test/test_end_to_end_integration.py::TestEndToEndIntegration::test_unified_scanner_initialization", "test/test_fixes_validation.py::test_caching_mechanism", "test/test_fixes_validation.py::test_market_data_validation", "test/test_fixes_validation.py::test_pivot_point_calculation", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_analyzer_initialization", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_calculation", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_calculation_with_offset", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_configuration_loading", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_error_handling", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_filter_disabled", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_filter_error_recovery", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_filter_integration_equity", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_filter_with_insufficient_data", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_mutual_exclusivity_with_pivot_point", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_price_passing_through", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_smoothing_configurations", "test/test_mae_indicator.py::TestMAEIndicator::test_mae_source_series_extraction", "test/test_mae_indicator.py::TestMAEIntegrationWithMarketScanners::test_mae_integration_with_futures_scanner", "test/test_mae_indicator.py::TestMAEIntegrationWithMarketScanners::test_mae_integration_with_index_scanner", "test/test_main_functionality.py::test_market_scanners", "test/test_main_functionality.py::test_options_filtering", "test/test_main_functionality.py::test_symbol_parsing", "test/test_main_functionality.py::test_unified_scanner_dry_run", "test/test_main_integration.py::test_main_dry_run", "test/test_main_integration.py::test_market_data_validation", "test/test_main_integration.py::test_pivot_point_integration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_all_symbols_configuration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_market_data_to_filtered_symbol_conversion", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_monthly_expiry_configuration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_pivot_point_calculations", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_pivot_point_filtering_integration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_symbol_parsing_edge_cases", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_weekly_symbol_parsing", "test/test_option_utils.py::TestOptionUtils::test_black_scholes_delta_call", "test/test_option_utils.py::TestOptionUtils::test_black_scholes_delta_edge_cases", "test/test_option_utils.py::TestOptionUtils::test_black_scholes_delta_itm_call", "test/test_option_utils.py::TestOptionUtils::test_black_scholes_delta_otm_call", "test/test_option_utils.py::TestOptionUtils::test_black_scholes_delta_put", "test/test_option_utils.py::TestOptionUtils::test_calculate_implied_volatility", "test/test_option_utils.py::TestOptionUtils::test_calculate_option_greeks", "test/test_option_utils.py::TestOptionUtils::test_format_option_symbol", "test/test_option_utils.py::TestOptionUtils::test_get_option_moneyness", "test/test_option_utils.py::TestOptionUtils::test_get_time_to_expiry", "test/test_option_utils.py::TestOptionUtils::test_is_weekly_expiry_valid", "test/test_option_utils.py::TestOptionUtils::test_parse_option_symbol", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_csv_generation_includes_delta_values", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_delta_based_filtering_creates_delta_map", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_delta_filtering_error_logging", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_delta_values_preserved_in_filtered_symbols", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_invalid_delta_values_handling", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_missing_delta_columns_handling", "test/test_options_prefiltering.py::test_options_prefiltering", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_batch_processing_optimization", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_cache_cleanup_functionality", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_fyers_connect_caching", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_memory_optimization_large_dataset", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_option_chain_caching", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_performance_regression_prevention", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_symbol_parsing_cache_performance", "test/test_pivot_calculation_types.py::test_pivot_calculation_types", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_handles_no_spot_price", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_preserves_ce_pe_balance", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_reduces_symbols_correctly", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_respects_strike_level_config", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_with_empty_symbols", "test/test_symbol_config.py::TestSymbolConfig::test_filter_symbols_by_pattern", "test/test_symbol_config.py::TestSymbolConfig::test_get_default_symbols", "test/test_symbol_config.py::TestSymbolConfig::test_get_symbol_config_default", "test/test_symbol_config.py::TestSymbolConfig::test_get_symbol_config_with_custom_symbols", "test/test_symbol_config.py::TestSymbolConfig::test_get_symbols_by_market_type", "test/test_symbol_config.py::TestSymbolConfig::test_get_symbols_by_market_type_invalid", "test/test_symbol_config.py::TestSymbolConfig::test_load_symbol_config_from_file", "test/test_symbol_config.py::TestSymbolConfig::test_merge_symbol_configs", "test/test_symbol_config.py::TestSymbolConfig::test_validate_symbol_config", "test/test_symbol_config.py::TestSymbolConfig::test_validate_symbol_config_invalid", "test/test_timeframe.py::test_fetch_intraday_data"]